"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import { FastForward, Pause, Play } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface VideoPlayerProps {
   src: string;
   poster?: string;
   name?: string;
   occupation?: string;
   className?: string;
}

export default function VideoPlayer({
   src,
   poster,
   name,
   occupation,
   className = "",
}: VideoPlayerProps) {
   const [isPlaying, setIsPlaying] = useState(false);
   const [currentTime, setCurrentTime] = useState(0);
   const [duration, setDuration] = useState(0);
   const [showControls, setShowControls] = useState(false);
   const videoRef = useRef<HTMLVideoElement>(null);
   const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   useEffect(() => {
      const video = videoRef.current;
      console.log("Duration: ", video?.duration);
      if (!video) return;

      const handleTimeUpdate = () => {
         setCurrentTime(video.currentTime);
      };

      const handleLoadedMetadata = () => {
         setDuration(video.duration);
      };
      const handleEnded = () => {
         setIsPlaying(false);
         setShowControls(false);
      };

      video.addEventListener("timeupdate", handleTimeUpdate);
      video.addEventListener("loadedmetadata", handleLoadedMetadata);
      video.addEventListener("ended", handleEnded);

      return () => {
         video.removeEventListener("timeupdate", handleTimeUpdate);
         video.removeEventListener("loadedmetadata", handleLoadedMetadata);
         video.removeEventListener("ended", handleEnded);
      };
   }, []);

   const togglePlay = () => {
      const video = videoRef.current;
      if (!video) return;

      if (isPlaying) {
         video.pause();
         setShowControls(false);
      } else {
         video.play();
         setShowControls(true);
      }
      setIsPlaying(!isPlaying);
   };

   const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
      const video = videoRef.current;
      if (!video) return;

      const newTime = Number.parseFloat(e.target.value);
      video.currentTime = newTime;
      setCurrentTime(newTime);
   };

   const skipForward = () => {
      const video = videoRef.current;
      if (!video) return;

      video.currentTime = Math.min(video.currentTime + 10, video.duration);
   };

   const skipBackward = () => {
      const video = videoRef.current;
      if (!video) return;

      video.currentTime = Math.max(video.currentTime - 10, 0);
   };

   const formatTime = (time: number) => {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
   };

   const handleMouseMove = () => {
      if (isPlaying) {
         setShowControls(true);

         if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
         }

         controlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
         }, 3000);
      }
   };

   return (
      <div
         className={`relative overflow-hidden bg-black ${className}`}
         onMouseMove={handleMouseMove}
         onMouseEnter={() => isPlaying && setShowControls(true)}
         onMouseLeave={() => isPlaying && setShowControls(false)}
      >
         <video
            ref={videoRef}
            src={src}
            poster={poster}
            className="h-full w-full object-cover"
            onClick={togglePlay}
         />

         {!isPlaying && (
            <div className="bg-opacity-40 absolute inset-0 flex items-center justify-center bg-black/50">
               <Button
                  onClick={togglePlay}
                  className="hover:bg-opacity-30 rounded-full bg-white/20 px-8 py-7 text-white backdrop-blur-lg hover:cursor-pointer"
               >
                  <div className="flex items-center justify-center gap-2">
                     <Play size={40} />
                     <span className="text-base font-medium text-white">
                        Play showreel
                     </span>
                  </div>
               </Button>
            </div>
         )}

         {!isPlaying && (
            <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 p-6 md:p-8">
               <div className="mx-auto max-w-[1380px] px-4 md:px-12">
                  <h1 className="text-2xl font-semibold text-white md:text-3xl lg:text-4xl">
                     {name}
                  </h1>
                  <p className="mt-2 text-base text-white/80">{occupation}</p>
               </div>
            </div>
         )}

         <div
            className={`absolute right-0 bottom-0 left-0 p-4 transition-opacity duration-300 ${
               showControls ? "opacity-100" : "opacity-0"
            }`}
         >
            <div className="mx-auto flex max-w-4xl items-center justify-center space-x-4 rounded-full border border-white/10 bg-black/50 p-4 pr-8 shadow-2xl backdrop-blur-xs">
               <Button
                  onClick={skipBackward}
                  variant="ghost"
                  size="icon"
                  className="hover:bg-opacity-20 text-white hover:bg-white"
               >
                  <FastForward size={20} className="rotate-180" />
               </Button>

               <Button
                  onClick={togglePlay}
                  variant="ghost"
                  size="icon"
                  className="hover:bg-opacity-20 text-white hover:bg-white"
               >
                  {isPlaying ? <Pause size={20} /> : <Play size={20} />}
               </Button>

               <Button
                  onClick={skipForward}
                  variant="ghost"
                  size="icon"
                  className="hover:bg-opacity-20 text-white hover:bg-white"
               >
                  <FastForward size={20} />
               </Button>

               <span className="text-sm text-white">
                  {formatTime(currentTime)}
               </span>

               <input
                  type="range"
                  min="0"
                  max={duration || 0}
                  value={currentTime}
                  onChange={handleSeek}
                  className="h-1 flex-grow appearance-none rounded-full bg-gray-600"
                  style={{
                     background: `linear-gradient(to right, white ${(currentTime / (duration || 1)) * 100}%, gray ${
                        (currentTime / (duration || 1)) * 100
                     }%)`,
                  }}
               />

               <span className="text-sm text-white">
                  {formatTime(duration)}
               </span>
            </div>
         </div>
      </div>
   );
}
