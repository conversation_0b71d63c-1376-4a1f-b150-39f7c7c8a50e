import { profilesList } from "@/data/sample-data";
import React from "react";
import ProfileCard from "./profile-card";

const monthNames = [
   "Jan",
   "Feb",
   "Mar",
   "Apr",
   "May",
   "Jun",
   "Jul",
   "Aug",
   "Sep",
   "Oct",
   "Nov",
   "Dec",
];

const currentDate = new Date();
const currentMonthIndex = currentDate.getMonth();
const currentYear = currentDate.getFullYear();

const ProfileList = () => {
   const upcomingMonth = monthNames[currentMonthIndex + 1];
   const pastMonths = monthNames.slice(0, currentMonthIndex).reverse();
   const futureMonths = monthNames.slice(currentMonthIndex + 2);
   const orderedMonths = [upcomingMonth, ...pastMonths, ...futureMonths];

   return (
      <div className="mx-auto max-w-[1380px] px-4 py-8">
         {orderedMonths.map((month, idx) => {
            const fullMonthName = new Date(
               currentYear,
               monthNames.indexOf(month),
            ).toLocaleString("default", { month: "long" });
            const profileData = profilesList.find(
               (p) => p.month === fullMonthName && p.year === currentYear,
            );

            let state: "past" | "upcoming" | "future";
            if (idx === 0) state = "upcoming";
            else if (idx < currentMonthIndex + 1) state = "past";
            else state = "future";

            return (
               <ProfileCard
                  key={month}
                  month={month}
                  data={profileData}
                  state={state}
               />
            );
         })}
      </div>
   );
};

export default ProfileList;
