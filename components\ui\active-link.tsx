"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

type ActiveLinkProps = {
   href: string;
   children: ReactNode;
   className?: string;
   exactMatch?: boolean;
};

export default function ActiveLink({
   href,
   children,
   className,
   exactMatch = false,
}: ActiveLinkProps) {
   const pathname = usePathname();
   const isActive = exactMatch
      ? pathname === href
      : pathname === href || pathname.startsWith(`${href}/`);

   return (
      <Link
         href={href}
         className={cn(
            "text-md relative font-medium transition-colors hover:text-white/80",
            !isActive && "text-white/70",
            className,
         )}
      >
         {children}
         {isActive && (
            <span className="absolute -bottom-0.5 left-0 h-[1px] w-full translate-y-full transform bg-white transition-transform" />
         )}
      </Link>
   );
}
