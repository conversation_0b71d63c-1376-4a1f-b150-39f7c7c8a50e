import { Profile } from "@/data/sample-data";
import Image from "next/image";

interface ProfileDetailsProps {
   profile: Profile;
}

export default function ProfileDetails({ profile }: ProfileDetailsProps) {
   return (
      <div className="grid-rows-[1fr, auto] grid grid-cols-1 gap-8 md:grid-cols-2 md:grid-rows-1">
         {/* Profile Image */}
         <div className="relative aspect-square w-full overflow-hidden bg-gray-100 md:aspect-auto md:h-full">
            <Image
               src={profile.image || "/placeholder.svg"}
               alt={profile.name}
               fill
               className="object-cover"
               priority
            />
         </div>

         {/* Profile Details */}
         <div className="bg-gray-100 p-8">
            <h1 className="mb-6 text-center text-3xl font-semibold uppercase">
               {profile.name}
            </h1>

            <div className="space-y-0 divide-y divide-gray-200 border-t border-gray-200">
               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Birth name:</span>
                  <span className="text-sm">{profile.birthName}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Alias:</span>
                  <span className="text-sm">{profile.alias}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Born:</span>
                  <span className="text-sm">{profile.dateOfBirth}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Nationality:</span>
                  <span className="text-sm">{profile.nationality}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Occupation:</span>
                  <span className="text-sm">{profile.occupation}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Years active:</span>
                  <span className="text-sm">{profile.yearsActive}</span>
               </div>

               <div className="grid grid-cols-2 py-4">
                  <span className="text-sm font-semibold">Awards:</span>
                  <span className="text-sm">{profile.awards}</span>
               </div>

               {profile.website && (
                  <div className="grid grid-cols-2 py-4">
                     <span className="text-sm font-semibold">Website:</span>
                     <a
                        href={`https://${profile.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline"
                     >
                        {profile.website}
                     </a>
                  </div>
               )}
            </div>
         </div>
      </div>
   );
}
