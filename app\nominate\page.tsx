import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "Nominate a Creative | Mlist",
   description:
      "Nominate exceptional creatives for our monthly list. Help us recognize outstanding talent across various creative fields.",
};

export default function NominatePage() {
   return (
      <main className="min-h-screen">
         <div className="relative bg-gray-100 py-16">
            <div className="mx-auto max-w-7xl px-4 md:px-8">
               <h1 className="text-3xl font-bold md:text-4xl">
                  Nominate a Creative
               </h1>
               <p className="mt-4 max-w-3xl text-lg text-gray-600">
                  Know someone exceptional in their creative field? Nominate
                  them for our monthly list. We&apos;re looking for individuals
                  who are pushing boundaries and redefining excellence.
               </p>
            </div>
         </div>

         <section className="mx-auto max-w-3xl px-4 py-12 md:px-8">
            <div className="rounded-lg bg-white p-8 shadow-md">
               <h2 className="mb-6 text-2xl font-semibold">Nomination Form</h2>
               <p className="mb-8 text-gray-600">
                  Please fill out the form below to nominate a creative for our
                  monthly list. All fields are required unless marked as
                  optional.
               </p>

               <form className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                     <div>
                        <label
                           htmlFor="nominator-name"
                           className="mb-2 block text-sm font-medium text-gray-700"
                        >
                           Your Name
                        </label>
                        <input
                           type="text"
                           id="nominator-name"
                           className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                           required
                        />
                     </div>
                     <div>
                        <label
                           htmlFor="nominator-email"
                           className="mb-2 block text-sm font-medium text-gray-700"
                        >
                           Your Email
                        </label>
                        <input
                           type="email"
                           id="nominator-email"
                           className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                           required
                        />
                     </div>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                     <div>
                        <label
                           htmlFor="nominee-name"
                           className="mb-2 block text-sm font-medium text-gray-700"
                        >
                           Nominee&apos;s Name
                        </label>
                        <input
                           type="text"
                           id="nominee-name"
                           className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                           required
                        />
                     </div>
                     <div>
                        <label
                           htmlFor="nominee-email"
                           className="mb-2 block text-sm font-medium text-gray-700"
                        >
                           Nominee&apos;s Email (Optional)
                        </label>
                        <input
                           type="email"
                           id="nominee-email"
                           className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                        />
                     </div>
                  </div>

                  <div>
                     <label
                        htmlFor="creative-field"
                        className="mb-2 block text-sm font-medium text-gray-700"
                     >
                        Creative Field
                     </label>
                     <select
                        id="creative-field"
                        className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                        required
                     >
                        <option value="">Select a field</option>
                        <option value="design">Design</option>
                        <option value="music">Music</option>
                        <option value="film">Film</option>
                        <option value="photography">Photography</option>
                        <option value="art">Art</option>
                        <option value="fashion">Fashion</option>
                        <option value="technology">Technology</option>
                        <option value="other">Other</option>
                     </select>
                  </div>

                  <div>
                     <label
                        htmlFor="nomination-reason"
                        className="mb-2 block text-sm font-medium text-gray-700"
                     >
                        Why are you nominating this person?
                     </label>
                     <textarea
                        id="nomination-reason"
                        rows={4}
                        className="w-full rounded-md border border-gray-300 px-4 py-2 focus:border-black focus:outline-none"
                        required
                     ></textarea>
                  </div>

                  <div>
                     <button
                        type="submit"
                        className="rounded-full bg-black px-8 py-3 text-sm font-medium text-white transition-colors hover:bg-gray-800"
                     >
                        Submit Nomination
                     </button>
                  </div>
               </form>
            </div>
         </section>
      </main>
   );
}
