const profileDescription =
   "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!";

const professionalStory =
   "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p>";

const backgroundStory =
   "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p></br><p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Corporis repellendus dolorum consequatur saepe? Sed tempore, earum ullam similique veniam corrupti a eveniet aut! Nesciunt voluptate beatae ipsa quas esse fuga, quidem quos laudantium eaque asperiores? Adipisci minima, consequatur possimus, nobis reiciendis dicta quidem ullam sunt nisi ducimus repellendus quae commodi!</p>";

export const editorsPick = [
   {
      name: "Alain Kabbi",
      image: "/images/editors-pick/editors-pick-main.png",
      description: profileDescription,
   },
   {
      name: "Alain Kabbi",
      image: "/images/editors-pick/editors-pick-sub-1.png",
      description: profileDescription,
   },
   {
      name: "Alain Kabbi",
      image: "/images/editors-pick/editors-pick-sub-2.png",
      description: profileDescription,
   },
   {
      name: "Alain Kabbi",
      image: "/images/editors-pick/editors-pick-sub-3.png",
      description: profileDescription,
   },
];

export const doubleNominatedProfiles = [
   {
      name: "TY Bello",
      image: "/images/headshots/headshot-7.png",
      description: profileDescription,
   },
   {
      name: "Meekdad Obafela",
      image: "/images/headshots/headshot-8.png",
      description: profileDescription,
   },
   {
      name: "Alain Kabbi",
      image: "/images/headshots/headshot-1.png",
      description: profileDescription,
   },
   {
      name: "TY Bello",
      image: "/images/headshots/headshot-2.png",
      description: profileDescription,
   },
   {
      name: "Meekdad Obafela",
      image: "/images/headshots/headshot-3.png",
      description: profileDescription,
   },
   {
      name: "Alain Kabbi",
      image: "/images/headshots/headshot-4.png",
      description: profileDescription,
   },
];

type ProfileSection =
   | "spotlighted"
   | "double-nominated"
   | "editors-pick"
   | "recently-added";

type Month =
   | "January"
   | "February"
   | "March"
   | "April"
   | "May"
   | "June"
   | "July"
   | "August"
   | "September"
   | "October"
   | "November"
   | "December";

export type Profile = {
   userId: string;
   slug: string;
   name: string;
   image: string;
   description: string;
   occupation: string;
   month: Month;
   year: number;
   sections?: ProfileSection[];
   birthName: string;
   alias: string;
   dateOfBirth: string;
   nationality: string;
   yearsActive: string;
   awards: string;
   showreel: string;
   professionalStory?: string;
   backgroundStory?: string;
   website?: string;
};

export const profilesList: Profile[] = [
   {
      userId: "1",
      slug: "ty-bello",
      name: "TY Bello",
      image: "/images/headshots/headshot-1.png",
      description: profileDescription,
      occupation: "Photographer",
      month: "January",
      year: 2025,
      birthName: "Toyin Sokefun-Bello",
      alias: "TY",
      dateOfBirth: "January 14, 1978",
      nationality: "Nigerian",
      yearsActive: "2000-present",
      awards: "Nigeria Photography Awards 2020, Best Portrait Photographer",
      showreel: "/videos/showreel-1.mp4",
      professionalStory: professionalStory,
      backgroundStory: backgroundStory,
      website: "www.tybello.com",
   },
   {
      userId: "2",
      slug: "meekdad-obafela",
      name: "Meekdad Obafela",
      image: "/images/headshots/headshot-2.png",
      description: profileDescription,
      occupation: "Visual Artist",
      month: "February",
      year: 2025,
      sections: ["spotlighted"],
      birthName: "Meekdad Obafela",
      alias: "MO Art",
      dateOfBirth: "March 22, 1985",
      nationality: "Nigerian",
      yearsActive: "2005-present",
      awards: "Lagos Art Exhibition Gold Medal 2022",
      showreel: "/videos/showreel-2.mp4",
      professionalStory: professionalStory,
      backgroundStory: backgroundStory,
   },
   {
      userId: "3",
      slug: "alain-kabbi",
      name: "Alain Kabbi",
      image: "/images/headshots/headshot-3.png",
      description: profileDescription,
      occupation: "Cinematographer",
      month: "March",
      year: 2025,
      sections: ["spotlighted"],
      birthName: "Alain Jean Kabbi",
      alias: "AK Films",
      dateOfBirth: "September 5, 1980",
      nationality: "Ivorian",
      yearsActive: "2003-present",
      awards: "AMAA Best Cinematography 2021",
      showreel: "/videos/showreel-3.mp4",
      professionalStory: professionalStory,
      backgroundStory: backgroundStory,
      website: "www.akfilms.com",
   },
   {
      userId: "4",
      slug: "moshood-obatula",
      name: "Moshood Obatula",
      image: "/images/headshots/headshot-4.png",
      description: profileDescription,
      occupation: "Music Producer",
      month: "April",
      year: 2025,
      sections: ["spotlighted"],
      birthName: "Moshood Ayinde Obatula",
      alias: "MO Beats",
      dateOfBirth: "April 15, 1990",
      nationality: "Nigerian",
      yearsActive: "2010-present",
      awards: "Producer of the Year 2024, Headies Awards",
      showreel: "/videos/showreel-1.mp4",
      professionalStory: professionalStory,
      backgroundStory: backgroundStory,
   },
   {
      userId: "5",
      slug: "jonathan-smith",
      name: "Jonathan Smith",
      image: "/images/headshots/headshot-5.png",
      description: profileDescription,
      occupation: "Fashion Designer",
      month: "May",
      year: 2025,
      birthName: "Jonathan Smith",
      alias: "MO Fashion",
      dateOfBirth: "July 30, 1988",
      nationality: "Nigerian",
      yearsActive: "2008-present",
      awards: "Africa Fashion Week Designer of the Year 2023",
      showreel: "/videos/showreel-2.mp4",
      professionalStory: professionalStory,
      backgroundStory: backgroundStory,
      website: "www.mofashion.com",
   },
];

const monthMap: Record<string, number> = {
   January: 0,
   February: 1,
   March: 2,
   April: 3,
   May: 4,
   June: 5,
   July: 6,
   August: 7,
   September: 8,
   October: 9,
   November: 10,
   December: 11,
};

export function getRecentProfiles(numberOfMonths: number): Profile[] {
   // Attach a real Date object to each profile
   const profilesWithDate = profilesList.map((profile) => ({
      ...profile,
      date: new Date(profile.year, monthMap[profile.month]),
   }));

   // Find the most recent date
   const latestDate = profilesWithDate.reduce(
      (latest, profile) => (profile.date > latest ? profile.date : latest),
      new Date(0),
   );

   // Compute the cutoff date
   const cutoffDate = new Date(latestDate);
   cutoffDate.setMonth(cutoffDate.getMonth() - (numberOfMonths - 1));

   // Filter, sort in reverse-chronological order, and return only the original profile fields
   return profilesWithDate
      .filter((profile) => profile.date >= cutoffDate)
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .map(({ ...profile }) => profile);
}

export function getProfilesBySection(section: ProfileSection): Profile[] {
   return profilesList.filter(
      (profile) => profile.sections?.includes(section) ?? false,
   );
}
