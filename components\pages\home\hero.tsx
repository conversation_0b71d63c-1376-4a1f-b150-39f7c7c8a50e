import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function Hero() {
   return (
      <section className="relative h-[500px] overflow-hidden md:h-[600px]">
         <div className="absolute inset-0 z-0">
            <Image
               src="/images/hero/hero-bg.png"
               alt="Creative professional"
               fill
               priority
               className="object-cover grayscale"
            />
         </div>
         <div className="relative z-10 mx-auto flex h-full max-w-7xl items-center justify-start px-4 md:px-8">
            <div className="w-[400px] md:w-[600px]">
               <Image
                  src="/images/hero/nominate-a-creative.png"
                  alt="Nominate a creative"
                  width={600}
                  height={600}
                  priority
               />
            </div>
         </div>
         <div className="relative mx-auto w-full max-w-7xl px-4 md:px-8">
            <Button
               asChild
               size="lg"
               className="text-md absolute bottom-12 left-0 z-20 mx-8 rounded-2xl border-2 border-gray-300 bg-black px-8 py-5 text-white hover:bg-gray-950 md:mx-8"
               variant="secondary"
            >
               <Link
                  href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                  target="_blank"
               >
                  Nominate
               </Link>
            </Button>
         </div>
      </section>
   );
}
