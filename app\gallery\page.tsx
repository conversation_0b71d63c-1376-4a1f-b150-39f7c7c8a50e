import GalleryGrid from "@/components/pages/gallery/gallery-grid";
import GalleryHero from "@/components/pages/gallery/gallery-hero";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "Gallery | Mlist",
   description:
      "Explore our gallery of professional headshots featuring exceptional creatives from the Mlist community.",
};

export default function GalleryPage() {
   return (
      <main className="min-h-screen">
         <GalleryHero />
         <div className="relative bg-gray-100 py-14">
            <div className="mx-auto max-w-[1380px] px-4 md:px-8">
               <h1 className="text-2xl font-bold md:text-3xl">Gallery</h1>
               <p className="text-md mt-4 text-gray-600">
                  Explore our collection of professional headshots featuring
                  exceptional creatives who have been recognized on Mlist. Each
                  image represents an individual who has made significant
                  contributions to their creative field.
               </p>
            </div>
         </div>
         <GalleryGrid />
      </main>
   );
}
