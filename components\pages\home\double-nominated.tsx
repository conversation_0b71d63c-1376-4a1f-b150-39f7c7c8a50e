import ProfileCard from "@/components/pages/home/<USER>";
import { doubleNominatedProfiles } from "@/data/sample-data";

export default function DoubleNominated() {
   return (
      <section className="mx-auto max-w-[1380px] px-4 py-12">
         <h2 className="mb-6 text-xl font-semibold">Double Nominated</h2>
         <div className="grid grid-cols-1 gap-6 gap-y-12 sm:grid-cols-2 lg:grid-cols-3">
            {doubleNominatedProfiles.map((profile, index) => (
               <ProfileCard
                  key={`double-${index}`}
                  name={profile.name}
                  image={profile.image}
                  description={profile.description}
               />
            ))}
         </div>
      </section>
   );
}
