"use client";

import VideoPlayer from "@/components/ui/video-player";
import { Profile } from "@/data/sample-data";

interface ProfileHeroProps {
   profile: Profile;
}

export default function ProfileHero({ profile }: ProfileHeroProps) {
   return (
      <section className="relative">
         {/* Video Showreel */}
         <div className="relative aspect-[16/10] max-h-[600px] w-full overflow-hidden">
            <VideoPlayer
               src={profile.showreel}
               poster={profile.image}
               name={profile.name}
               occupation={profile.occupation}
               className="h-full w-full"
            />
         </div>
      </section>
   );
}
