"use client";

import ImageModal from "@/components/pages/gallery/image-modal";
import { profilesList } from "@/data/sample-data";
import Image from "next/image";

export default function GalleryGrid() {
   // Create a more extensive gallery by duplicating profiles
   const galleryImages = [
      ...profilesList,
      ...profilesList.map((profile) => ({
         ...profile,
         userId: `${profile.userId}-duplicate-1`,
      })),
      ...profilesList.map((profile) => ({
         ...profile,
         userId: `${profile.userId}-duplicate-2`,
      })),
      ...profilesList.map((profile) => ({
         ...profile,
         userId: `${profile.userId}-duplicate-3`,
      })),
   ];

   return (
      <section className="mx-auto max-w-[1380px] px-4 py-12 md:px-8">
         <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {galleryImages.map((profile) => (
               <div
                  key={`gallery-${profile.userId}`}
                  className="group overflow-hidden"
               >
                  <ImageModal
                     src={profile.image}
                     alt={`${profile.name}, ${profile.occupation}`}
                     name={profile.name}
                     occupation={profile.occupation}
                  >
                     <div className="relative aspect-square w-full cursor-pointer overflow-hidden">
                        <Image
                           src={profile.image}
                           alt={`${profile.name}, ${profile.occupation}`}
                           fill
                           className="object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/0 transition-colors duration-300 group-hover:bg-black/20"></div>
                     </div>
                  </ImageModal>
               </div>
            ))}
         </div>
      </section>
   );
}
