interface BackgroundStoryProps {
   story?: string;
}

export default function BackgroundStory({ story }: BackgroundStoryProps) {
   if (!story) return null;

   return (
      <section className="mt-10">
         <h2 className="mb-6 border-b border-gray-200 pb-4 text-2xl font-semibold">
            Background Story
         </h2>
         <div
            className="prose max-w-none text-gray-600"
            dangerouslySetInnerHTML={{
               __html: story,
            }}
         />
      </section>
   );
}
