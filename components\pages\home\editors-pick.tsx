import { editorsPick } from "@/data/sample-data";
import Image from "next/image";

export default function EditorsPick() {
   const EditorMain = editorsPick[0];

   return (
      <section className="mx-auto max-w-[1380px] px-4 py-12">
         <h2 className="mb-6 text-xl font-semibold">The Pick</h2>
         <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
            <div className="col-span-2 lg:col-span-2">
               <div className="relative h-[400px] w-full overflow-hidden lg:h-full">
                  <Image
                     src={EditorMain.image || "/placeholder.svg"}
                     alt={EditorMain.name}
                     fill
                     className="object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black p-6">
                     <h3 className="text-xl font-semibold text-white">
                        {EditorMain.name}
                     </h3>
                     <p className="mt-1 line-clamp-2 text-sm text-gray-300">
                        {EditorMain.description}
                     </p>
                  </div>
               </div>
            </div>

            <div className="col-span-2 flex flex-col space-y-6">
               {editorsPick.slice(1).map((profile, index) => (
                  <div
                     className="flex items-center space-x-4"
                     key={`editor-${index}`}
                  >
                     <div className="relative h-40 w-40 flex-shrink-0 overflow-hidden">
                        <Image
                           src={profile.image || "/placeholder.svg"}
                           alt={profile.name}
                           fill
                           className="object-cover transition-transform duration-300 hover:scale-105"
                        />
                     </div>
                     <div>
                        <h3 className="text-lg font-semibold">
                           {profile.name}
                        </h3>
                        <p className="text-md mt-1 line-clamp-3 text-gray-600">
                           {profile.description}
                        </p>
                     </div>
                  </div>
               ))}
            </div>
         </div>
      </section>
   );
}
