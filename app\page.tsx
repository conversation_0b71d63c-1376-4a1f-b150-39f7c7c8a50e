import DoubleNominated from "@/components/pages/home/<USER>";
import EditorsPick from "@/components/pages/home/<USER>";
import Hero from "@/components/pages/home/<USER>";
import RecentlyAdded from "@/components/pages/home/<USER>";
import Spotlighted from "@/components/pages/home/<USER>";

function Home() {
   return (
      <main className="min-h-screen">
         <Hero />
         <RecentlyAdded />
         <hr className="mx-4 h-px border-0 bg-gray-300 md:mx-20" />
         <Spotlighted />
         <hr className="mx-4 h-px border-0 bg-gray-300 md:mx-20" />
         <EditorsPick />
         <hr className="mx-4 h-px border-0 bg-gray-300 md:mx-20" />
         <DoubleNominated />
      </main>
   );
}

export default Home;
