"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Dialog,
   DialogClose,
   DialogContent,
   DialogOverlay,
   DialogPortal,
   DialogTitle,
   DialogTrigger,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

interface ImageModalProps {
   src: string;
   alt: string;
   name?: string;
   occupation?: string;
   children: React.ReactNode;
}

export default function ImageModal({
   src,
   alt,
   name,
   occupation,
   children,
}: ImageModalProps) {
   const [isOpen, setIsOpen] = useState(false);

   // Handle ESC key press
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         if (e.key === "Escape" && isOpen) {
            setIsOpen(false);
         }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
   }, [isOpen]);

   return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
         <DialogTrigger asChild>{children}</DialogTrigger>
         <DialogPortal>
            <DialogOverlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 bg-black/80 backdrop-blur-sm" />
            <DialogContent
               closeIcon={false}
               className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 border-none bg-transparent p-0 shadow-none"
               onEscapeKeyDown={() => setIsOpen(false)}
            >
               {/* Visually hidden title for accessibility */}
               <DialogTitle className="sr-only">
                  {name ? `${name}'s headshot` : "Headshot image"}
               </DialogTitle>

               <div className="relative flex items-center justify-center">
                  <div className="relative flex items-center justify-center overflow-hidden rounded-md">
                     <Image
                        src={src}
                        alt={alt}
                        className="object-cover"
                        width={1200}
                        height={1200}
                        priority
                     />
                     {(name || occupation) && (
                        <div className="absolute right-0 bottom-0 left-0 bg-gradient-to-t from-black/80 p-6">
                           {name && (
                              <h3 className="text-xl font-semibold text-white">
                                 {name}
                              </h3>
                           )}
                           {occupation && (
                              <p className="mt-1 text-sm text-gray-300">
                                 {occupation}
                              </p>
                           )}
                        </div>
                     )}
                  </div>
                  <DialogClose asChild>
                     <Button
                        size="icon"
                        className="absolute top-2 right-2 rounded-full bg-black/50 text-white hover:cursor-pointer hover:bg-black/70"
                        aria-label="Close dialog"
                     >
                        <X className="h-5 w-5" />
                     </Button>
                  </DialogClose>
               </div>
            </DialogContent>
         </DialogPortal>
      </Dialog>
   );
}
