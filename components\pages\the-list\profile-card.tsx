import { Profile } from "@/data/sample-data";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function ProfileCard({
   month,
   data,
   state,
}: {
   month: string;
   data?: Profile;
   state: "past" | "upcoming" | "future";
}) {
   const isNominateAvailable = state === "upcoming";

   return (
      <div className="flex items-start gap-4 border-b border-gray-200 py-8">
         <div
            className={`flex h-10 w-12 flex-shrink-0 items-center justify-center text-sm font-semibold text-white ${
               state === "future" ? "bg-gray-300" : "bg-black"
            }`}
         >
            {month}
         </div>

         {data ? (
            <div className="flex w-full flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
               <div className="flex items-center gap-4">
                  <div className="relative h-50 w-50 flex-shrink-0 overflow-hidden">
                     <Image
                        src={data.image}
                        alt={data.name}
                        fill
                        className="object-cover"
                     />
                  </div>
                  <div className="space-y-2">
                     <h2 className="text-lg font-semibold">{data.name}</h2>
                     {/* <p className="text-md font-semibold text-gray-700"> */}
                     <p className="text-md font-semibold text-gray-500">
                        {data.occupation}
                     </p>
                     <p className="mr-4 line-clamp-3 text-gray-500">
                        {data.description}
                     </p>
                     <Link
                        href={`/profiles/${data.slug}`}
                        className="flex cursor-pointer items-center gap-2 text-sm text-blue-500"
                     >
                        Read more
                        <ArrowRight size={14} />
                     </Link>
                  </div>
               </div>

               <Link
                  href={`/profiles/${data.slug}`}
                  className="ml-auto rounded-full bg-black px-6 py-3 text-sm whitespace-nowrap text-white shadow-md shadow-gray-400 focus:ring-2 focus:ring-black focus:ring-offset-2 focus:outline-none"
                  aria-label={`View profile for ${data.name}`}
               >
                  View Profile
               </Link>
            </div>
         ) : (
            <div className="flex w-full flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
               <div className="flex w-full items-center gap-4">
                  <div className="h-50 w-50 bg-gray-200" />
                  <div className="mr-8 grow space-y-2">
                     <h2 className="text-md font-bold text-gray-500">TBD</h2>
                     <p className="text-sm text-gray-400">
                        {isNominateAvailable
                           ? `Nominations open for ${month} 2025`
                           : `${month} 2025 - Coming soon`}
                     </p>
                     <div className="space-y-2 py-2">
                        <div className="h-4 w-full rounded-sm bg-gray-200" />
                        <div className="h-4 w-full rounded-sm bg-gray-200" />
                        <div className="h-4 w-full rounded-sm bg-gray-200" />
                     </div>
                  </div>
               </div>

               <button
                  className={`ml-auto rounded-full bg-black px-6 py-3 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none ${
                     isNominateAvailable
                        ? "bg-black text-white shadow-md shadow-gray-400 hover:bg-gray-800 focus:ring-black"
                        : "cursor-not-allowed bg-gray-200 text-gray-400 focus:ring-transparent"
                  }`}
                  disabled={!isNominateAvailable}
                  aria-label={
                     isNominateAvailable
                        ? "Nominate a profile"
                        : "Nomination not available"
                  }
               >
                  Nominate
               </button>
            </div>
         )}
      </div>
   );
}
