import BackgroundStory from "@/components/pages/profiles/background-story";
import ProfessionalStory from "@/components/pages/profiles/professional-story";
import ProfileDetails from "@/components/pages/profiles/profile-details";
import ProfileHero from "@/components/pages/profiles/profile-hero";
import { profilesList } from "@/data/sample-data";
import type { Metadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";

type Props = {
   params: Promise<{ slug: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
   const { slug } = await params;
   const profile = profilesList.find((p) => p.slug === slug);

   if (!profile) {
      return {
         title: "Profile Not Found | Mlist",
      };
   }

   return {
      title: `${profile.name} | Mlist`,
      description: `Learn more about ${profile.name}, ${profile.occupation}. ${profile.description.substring(0, 150)}...`,
   };
}

export async function generateStaticParams() {
   return profilesList.map((profile) => ({
      slug: profile.slug,
   }));
}

export default async function ProfilePage({ params }: Props) {
   const { slug } = await params;
   const profile = profilesList.find((p) => p.slug === slug);

   if (!profile) {
      notFound();
   }

   return (
      <main className="min-h-screen">
         <ProfileHero profile={profile} />
         <div className="mx-auto max-w-[1380px] px-4 py-12 md:px-8">
            <div className="px-4">
               <ProfileDetails profile={profile} />

               {profile.professionalStory && (
                  <ProfessionalStory story={profile.professionalStory} />
               )}

               {profile.backgroundStory && (
                  <BackgroundStory story={profile.backgroundStory} />
               )}

               {/* Back to List */}
               <div className="mt-12">
                  <Link
                     href="/the-list"
                     className="text-sm text-gray-600 hover:text-black"
                  >
                     ← Back to The List
                  </Link>
               </div>
            </div>
         </div>
      </main>
   );
}
