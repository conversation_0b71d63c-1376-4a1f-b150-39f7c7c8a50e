import Image from "next/image";
import Link from "next/link";

type Props = {
   name: string;
   image?: string;
   description: string;
   slug?: string;
};

export default function ProfileCard({ name, image, description, slug }: Props) {
   const content = (
      <>
         <div className="relative h-80 w-full overflow-hidden">
            <Image
               src={image || "/placeholder.svg"}
               alt={name}
               fill
               className="object-cover transition-transform duration-300 hover:scale-105"
            />
         </div>
         <div className="mt-3">
            <h3 className="text-lg font-semibold">{name}</h3>
            <p className="text-md mt-1 line-clamp-2 text-gray-600">
               {description}
            </p>
         </div>
      </>
   );

   if (slug) {
      return (
         <Link href={`/profiles/${slug}`} className="block overflow-hidden">
            {content}
         </Link>
      );
   }

   return <div className="overflow-hidden">{content}</div>;
}
