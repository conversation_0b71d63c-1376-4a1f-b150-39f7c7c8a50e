import ActiveLink from "@/components/ui/active-link";
import Image from "next/image";
import Link from "next/link";

export default function Navigation() {
   return (
      <header className="bg-black text-white">
         <div className="mx-auto max-w-7xl px-4 md:px-8">
            <div className="flex h-24 items-center justify-between">
               <Link href="/" className="flex items-center">
                  <div className="relative mr-2 h-18 w-18">
                     <Image
                        src="/images/logo.png"
                        alt="Mlist Logo"
                        fill
                        className="object-contain"
                     />
                  </div>
               </Link>
               <nav className="hidden items-center space-x-8 md:flex">
                  <ActiveLink href="/" exactMatch>
                     Home
                  </ActiveLink>
                  <ActiveLink href="/the-list">The List</ActiveLink>
                  <ActiveLink href="/profiles">Profiles</ActiveLink>
                  <ActiveLink href="/gallery">Gallery</ActiveLink>
                  <ActiveLink href="/magazine">Magazine</ActiveLink>
                  <ActiveLink href="/newsletter">Newsletter</ActiveLink>
               </nav>
               <div className="md:hidden">
                  {/* Mobile menu button would go here */}
                  <button className="p-2">
                     <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-menu"
                     >
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                     </svg>
                  </button>
               </div>
            </div>
         </div>
      </header>
   );
}
