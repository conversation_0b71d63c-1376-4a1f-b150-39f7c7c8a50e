import Hero from "@/components/pages/the-list/hero";
import ProfileList from "@/components/pages/the-list/profile-list";
import type { Metada<PERSON> } from "next";

export const metadata: Metadata = {
   title: "The List | Mlist",
   description:
      "Explore our monthly lists of outstanding creatives. Each month, we highlight exceptional talent across various creative fields.",
};

export default function TheListPage() {
   return (
      <main className="min-h-screen">
         <Hero />
         <div className="relative bg-gray-100 py-14">
            <div className="mx-auto max-w-[1380px] px-4 md:px-8">
               <h1 className="text-2xl font-bold md:text-3xl">The List</h1>
               <p className="text-md mt-4 text-gray-600">
                  Discover exceptional creatives who have made our monthly
                  lists. Each month, we celebrate individuals who are pushing
                  boundaries and redefining excellence in their fields.
               </p>
            </div>
         </div>
         <ProfileList />
      </main>
   );
}
