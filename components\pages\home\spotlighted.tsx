import ProfileCard from "@/components/pages/home/<USER>";
import { getProfilesBySection } from "@/data/sample-data";

export default function Spotlighted() {
   const spotlightedProfiles = getProfilesBySection("spotlighted");

   return (
      <section className="mx-auto max-w-[1380px] px-4 py-12">
         <h2 className="mb-6 text-xl font-semibold">Spotlighted Profiles</h2>
         <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {spotlightedProfiles.map((profile, index) => (
               <ProfileCard
                  key={`spotlight-${index}`}
                  name={profile.name}
                  image={profile.image}
                  description={profile.description}
                  slug={profile.slug}
               />
            ))}
         </div>
      </section>
   );
}
