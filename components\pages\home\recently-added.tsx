import ProfileCard from "@/components/pages/home/<USER>";
import { getRecentProfiles } from "@/data/sample-data";

export default function RecentlyAdded() {
   const recentlyAddedProfiles = getRecentProfiles(3);

   return (
      <section className="mx-auto max-w-[1380px] px-4 py-12">
         <h2 className="mb-4 text-xl font-semibold">Recently Added</h2>
         <div className="grid grid-cols-1 gap-7 sm:grid-cols-2 lg:grid-cols-3">
            {recentlyAddedProfiles.map((profile, index) => (
               <ProfileCard
                  key={`recent-${index}`}
                  name={profile.name}
                  image={profile.image}
                  description={profile.description}
                  slug={profile.slug}
               />
            ))}
         </div>
      </section>
   );
}
